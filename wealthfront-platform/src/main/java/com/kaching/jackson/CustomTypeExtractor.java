package com.kaching.jackson;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.fasterxml.jackson.databind.JavaType;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;
import com.kaching.platform.functional.Unchecked;
import com.kaching.util.functional.Tuple2;
import com.twolattes.json.Entity;
import com.twolattes.json.MarshalledBy;
import com.twolattes.json.Value;
import com.twolattes.json.types.JsonType;

public class CustomTypeExtractor {

  static CustomTypes getCustomTypesFromEntityFieldsAndSubtypes(Class<?> clazz) {
    return getCustomTypesFromEntityFieldsAndSubtypes(clazz, new HashSet<>());
  }

  private static CustomTypes getCustomTypesFromEntityFieldsAndSubtypes(
      Class<?> clazz, Set<Class<?>> entityClassesAlreadyProcessed) {
    Preconditions.checkArgument(
        clazz.getAnnotation(Entity.class) != null,
        "Received non-Entity %s", clazz.getSimpleName()
    );

    Map<Class<?>, JsonType<?, ?>> typeMappingsFromFieldAnnotations = new HashMap<>();
    Set<Class<?>> mapKeyEntityClasses = new HashSet<>();
    entityClassesAlreadyProcessed.add(clazz);

    List<Field> entityFields = new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()));

    Class<?> superclass = clazz.getSuperclass();
    while (superclass != null) {
      entityFields.addAll(Arrays.asList(superclass.getDeclaredFields()));
      superclass = superclass.getSuperclass();
    }

    for (Class<?> subclass : clazz.getAnnotation(Entity.class).subclasses()) {
      entityFields.addAll(Arrays.asList(subclass.getDeclaredFields()));

      Class<?> subclassSuperclass = subclass.getSuperclass();
      while (!subclassSuperclass.equals(clazz)) {
        entityFields.addAll(Arrays.asList(subclassSuperclass.getDeclaredFields()));
        subclassSuperclass = subclassSuperclass.getSuperclass();
      }
    }

    for (Field field : entityFields) {
      Value valueAnnotation = field.getAnnotation(Value.class);
      if (valueAnnotation != null) {
        ArrayList<Class<? extends JsonType>> jsonTypeClasses = new ArrayList<>();
        if (valueAnnotation.type() != JsonType.class) {
          jsonTypeClasses.add(valueAnnotation.type());
        }
        jsonTypeClasses.addAll(Arrays.asList(valueAnnotation.types()));

        if (field.getType().getAnnotation(MarshalledBy.class) != null) {
          jsonTypeClasses.add(field.getType().getAnnotation(MarshalledBy.class).value());
        }

        for (Class<? extends JsonType> jsonTypeClass : jsonTypeClasses) {
          int interfaceIndex = Arrays.asList(jsonTypeClass.getInterfaces()).indexOf(JsonType.class);
          ParameterizedType genericJsonTypeClass = (ParameterizedType) (interfaceIndex >= 0 ?
              jsonTypeClass.getGenericInterfaces()[interfaceIndex] : jsonTypeClass.getGenericSuperclass());
          Type entityType = genericJsonTypeClass.getActualTypeArguments()[0];
          Class<?> actualTypeArgumentClass =
              (Class<?>) (entityType instanceof ParameterizedType ? ((ParameterizedType) entityType).getRawType() : entityType);
          JsonType<?, ?> jsonTypeInstance = Unchecked.get(() -> {
            Constructor<? extends JsonType> constructor = jsonTypeClass.getDeclaredConstructor();
            constructor.setAccessible(true);
            return constructor.newInstance();
          });
          typeMappingsFromFieldAnnotations.put(actualTypeArgumentClass, jsonTypeInstance);
        }

        EntityClasses allEntitiesInType = getAllEntitiesInType(field.getGenericType());
        mapKeyEntityClasses.addAll(allEntitiesInType.getMapKeys());
        for (Class<?> entityClass : allEntitiesInType.getAll()) {
          if (!entityClassesAlreadyProcessed.contains(entityClass)) {
            CustomTypes fromEntityFieldsAndSubtypes =
                getCustomTypesFromEntityFieldsAndSubtypes(entityClass, entityClassesAlreadyProcessed);
            typeMappingsFromFieldAnnotations.putAll(fromEntityFieldsAndSubtypes.getTypesWithJsonTypes());
            mapKeyEntityClasses.addAll(fromEntityFieldsAndSubtypes.getMapKeyEntityClasses());
          }
        }
      }
    }

    return new CustomTypes(typeMappingsFromFieldAnnotations, mapKeyEntityClasses);
  }

  static EntityClasses getAllEntitiesInType(Type type) {
    Set<Class<?>> all = new HashSet<>();
    Set<Class<?>> mapKeys = new HashSet<>();

    if (type instanceof GenericArrayType) {
      GenericArrayType genericArrayType = (GenericArrayType) type;
      type = genericArrayType.getGenericComponentType();
    }

    if (type instanceof ParameterizedType) {
      ParameterizedType parameterizedType = (ParameterizedType) type;
      for (Type typeArgument : parameterizedType.getActualTypeArguments()) {
        EntityClasses entitiesInType = getAllEntitiesInType(typeArgument);
        all.addAll(entitiesInType.getAll());
        mapKeys.addAll(entitiesInType.getMapKeys());
      }

      JavaType potentialKeyType = DefaultJacksonMappers.getKeyType(type);
      if (potentialKeyType != null) {
        if (potentialKeyType.getRawClass().getAnnotation(Entity.class) != null) {
          mapKeys.add(potentialKeyType.getRawClass());
        }
      }

      type = parameterizedType.getRawType();
    }

    if (type instanceof Class) {
      Class rawType = (Class) type;
      if (rawType.getAnnotation(Entity.class) != null) {
        all.add(rawType);
      }
    }

    return new EntityClasses(all, mapKeys);
  }

  static class EntityClasses extends Tuple2<Set<Class<?>>, Set<Class<?>>> {

    EntityClasses(Set<Class<?>> all, Set<Class<?>> mapKeys) {
      super(all, mapKeys);
    }

    public Set<Class<?>> getAll() {
      return _1;
    }

    public Set<Class<?>> getMapKeys() {
      return _2;
    }

  }

  static class CustomTypes extends Tuple2<Map<Class<?>, JsonType<?, ?>>, Set<Class<?>>> {

    @VisibleForTesting
    CustomTypes(Map<Class<?>, JsonType<?, ?>> typesWithJsonTypes, Set<Class<?>> mapKeyEntityClasses) {
      super(typesWithJsonTypes, mapKeyEntityClasses);
    }

    public Map<Class<?>, JsonType<?, ?>> getTypesWithJsonTypes() {
      return _1;
    }

    public Set<Class<?>> getMapKeyEntityClasses() {
      return _2;
    }

    public CustomTypes combine(CustomTypes other) {
      Map<Class<?>, JsonType<?, ?>> newMap = new HashMap<>();
      newMap.putAll(this.getTypesWithJsonTypes());
      newMap.putAll(other.getTypesWithJsonTypes());
      Set<Class<?>> newSet = Sets.union(this.getMapKeyEntityClasses(), other.getMapKeyEntityClasses());
      return new CustomTypes(newMap, newSet);
    }

  }

}
