package com.kaching.platform.mattermost;

import static com.kaching.entities.EmailAddress.emailAddress;

import java.util.ArrayList;
import java.util.List;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.kaching.Author;
import com.kaching.platform.common.Option;
import com.kaching.platform.pagerduty.OnCallDirectory;
import com.kaching.platform.pagerduty.OnCallRotation;
import com.kaching.platform.pagerduty.api.v2.User;

public class OnCallHipChatMentioner {

  private static final String DEFAULT_MENTION = "all";
  private static final String DEFAULT_ROOM = "esp-party";

  private Provider<OnCallDirectory> onCallDirectory;
  private Provider<MattermostUserDirectory> mattermostUserDirectory;

  @Inject
  public OnCallHipChatMentioner(
      Provider<OnCallDirectory> onCallDirectory, Provider<MattermostUserDirectory> mattermostUserDirectory) {
    this.onCallDirectory = onCallDirectory;
    this.mattermostUserDirectory = mattermostUserDirectory;
  }

  public Option<HipChatMention> mentionCurrentOnCallFor(Author author) {
    Option<HipChatMention> maybeTeamMention = OnCallRotation.fromAuthor(author).transform(rotation -> {
      String room = rotation.getHipChatRoom();
      List<User> pdUsers = onCallDirectory.get().getTiersFor(rotation);
      String tierOne;
      if (pdUsers.isEmpty()) {
        tierOne = DEFAULT_MENTION;
      } else {
        User tierOneUser = pdUsers.get(0);
        tierOne = getMentionName(tierOneUser).getOrElse(DEFAULT_MENTION);
      }
      List<String> ccs = new ArrayList<>();
      if (pdUsers.size() >= 2) {
        getMentionName(pdUsers.get(1))
            .ifDefined(ccs::add);
      }
      return HipChatMention.of(room, tierOne, ccs);
    });
    if (maybeTeamMention.isDefined()) {
      return maybeTeamMention;
    } else {
      return lookupNonTeam(author);
    }
  }

  public Option<HipChatMention> lookupNonTeam(Author author) {
    return mattermostUserDirectory.get().findFromEmail(emailAddress(author.getPrimaryEmailAddress()))
        .transform(mattermostUser -> HipChatMention.of(DEFAULT_ROOM, mattermostUser.getUsername()));
  }

  private Option<String> getMentionName(User pagerDutyUser) {
    return mattermostUserDirectory.get().findFromEmail(emailAddress(pagerDutyUser.getEmail()))
        .transform(MattermostUser::getUsername);
  }

}
