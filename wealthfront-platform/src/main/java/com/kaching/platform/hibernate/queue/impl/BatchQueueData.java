package com.kaching.platform.hibernate.queue.impl;

import static com.google.common.base.Preconditions.checkArgument;
import static com.google.common.collect.ImmutableSortedMap.toImmutableSortedMap;

import java.util.Comparator;
import java.util.List;
import java.util.SortedMap;

import com.google.common.collect.ContiguousSet;
import com.google.common.collect.Range;
import com.kaching.DefaultKachingMarshallers;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.wealthfront.util.objects.DerivedMethods;

public class BatchQueueData {

  @Entity
  public static class DeflatedBatch implements Comparable<DeflatedBatch> {
    
    public static final Marshaller<DeflatedBatch> MARSHALLER = DefaultKachingMarshallers.createMarshaller(DeflatedBatch.class);
    
    private static final DerivedMethods<DeflatedBatch> DERIVED_METHODS = new DerivedMethods<>(DeflatedBatch.class);

    @Value private Id<BatchQueueBatch> batchId;
    @Value private Id<BatchQueueItem> firstItemId;
    @Value private Id<BatchQueueItem> lastItemId;
    @Value private int size;
    @Value private Json.Value batchData;
    @Value private int tryNumber;
    @Value private Integer retryBatchSize;
    
    DeflatedBatch() { /* JSON */ }

    public DeflatedBatch(Id<BatchQueueBatch> batchId, Id<BatchQueueItem> firstItemId, Id<BatchQueueItem> lastItemId, int size,
                         Json.Value batchData, int tryNumber, Option<Integer> retryBatchSize) {
      this.batchId = batchId;
      this.firstItemId = firstItemId;
      this.lastItemId = lastItemId;
      this.size = size;
      this.batchData = batchData;
      this.tryNumber = tryNumber;
      this.retryBatchSize = retryBatchSize.getOrNull();
    }

    public Id<BatchQueueBatch> getBatchId() {
      return batchId;
    }
    
    public Id<BatchQueueItem> getFirstItemId() {
      return firstItemId;
    }
    
    public Id<BatchQueueItem> getLastItemId() {
      return lastItemId;
    }
    
    public Range<Id<BatchQueueItem>> getItemIdRange() {
      return Range.closed(firstItemId, lastItemId);
    }
    
    public ContiguousSet<Id<BatchQueueItem>> getItemIdSet() {
      return ContiguousSet.create(getItemIdRange(), BatchQueueItem.DISCRETE_DOMAIN);
    }

    public int getSize() {
      return size;
    }

    public Json.Value getBatchData() {
      return batchData;
    }

    public int getTryNumber() {
      return tryNumber;
    }
    
    public Option<Integer> getRetryBatchSize() {
      return Option.of(retryBatchSize);
    }
    
    public DeflatedBatch incrementTryNumber() {
      return new DeflatedBatch(batchId, firstItemId, lastItemId, size, batchData, tryNumber + 1, Option.none());
    }
    
    public DeflatedBatch setRetryBatchSize(int retryBatchSize) {
      return new DeflatedBatch(batchId, firstItemId, lastItemId, size, batchData, tryNumber, Option.some(retryBatchSize));
    }
    
    @Override
    public boolean equals(Object obj) {
      return DERIVED_METHODS.equals(this, obj);
    }
    
    @Override
    public int hashCode() {
      return DERIVED_METHODS.hashCode(this);
    }
    
    @Override
    public String toString() {
      return DERIVED_METHODS.toString(this);
    }
    
    public int compareTo(DeflatedBatch o) {
      if (this == o) {
        return 0;
      }
      int tryNumberCompare = -Integer.compare(tryNumber, o.tryNumber);
      if (tryNumberCompare != 0) {
        return tryNumberCompare;
      }
      int firstItemIdCompare = Long.compare(firstItemId.asLong(), o.firstItemId.asLong());
      if (firstItemIdCompare != 0) {
        return firstItemIdCompare;
      }
      return getArbitraryComparisonForEqualsConsistency(o);
    }
    
    private int getArbitraryComparisonForEqualsConsistency(DeflatedBatch o) {
      int lastItemIdCompare = Long.compare(lastItemId.asLong(), o.lastItemId.asLong());
      if (lastItemIdCompare != 0) {
        return lastItemIdCompare;
      }
      int sizeCompare = Integer.compare(size, o.size);
      if (sizeCompare != 0) {
        return sizeCompare;
      }
      int batchDataCompare = batchData.toString().compareTo(o.batchData.toString());
      if (batchDataCompare != 0) {
        return batchDataCompare;
      }
      int retryBatchSizeCompare = Integer.compare(retryBatchSize, o.retryBatchSize);
      if (retryBatchSizeCompare != 0) {
        return retryBatchSizeCompare;
      }
      return batchId.compareTo(o.batchId);
    } 

  }
  
  public static class BatchItem<T> {
    
    private final Id<BatchQueueItem> id;
    private final T item;
    
    public BatchItem(Id<BatchQueueItem> id, T item) {
      this.id = id;
      this.item = item;
    }
    
    public Id<BatchQueueItem> getId() {
      return id;
    }

    public T getItem() {
      return item;
    }
    
  }

  public static class InflatedBatch<B, I> {
    
    private final Id<BatchQueueBatch> id;
    private final B batchData;
    private final Json.Value batchDataSerialized;
    private final List<BatchItem<I>> items;
    private final int tryNumber;

    public InflatedBatch(Id<BatchQueueBatch> id,
                         B batchData,
                         Json.Value batchDataSerialized,
                         List<BatchItem<I>> items,
                         int tryNumber) {
      checkArgument(!items.isEmpty(), "Cannot create an empty batch");
      this.id = id;
      this.batchData = batchData;
      this.batchDataSerialized = batchDataSerialized;
      this.items = items;
      this.tryNumber = tryNumber;
    }

    public Id<BatchQueueBatch> getId() {
      return id;
    }

    public B getBatchData() {
      return batchData;
    }

    public Json.Value getBatchDataSerialized() {
      return batchDataSerialized;
    }

    public List<BatchItem<I>> getItems() {
      return items;
    }
    
    public SortedMap<Id<BatchQueueItem>, I> getItemsAsMap() {
      return items.stream().collect(toImmutableSortedMap(Comparator.naturalOrder(), BatchItem::getId, BatchItem::getItem));
    }

    public int getTryNumber() {
      return tryNumber;
    }
    
    public DeflatedBatch deflate() {
      Id<BatchQueueItem> firstItemId = null;
      Id<BatchQueueItem> lastItemId = null;
      for (BatchItem<I> item : items) {
        if (firstItemId == null || item.getId().compareTo(firstItemId) < 0) {
          firstItemId = item.getId();
        }
        if (lastItemId == null || item.getId().compareTo(lastItemId) > 0) {
          lastItemId = item.getId();
        }
      }
      return new DeflatedBatch(id, firstItemId, lastItemId, items.size(), batchDataSerialized, tryNumber, Option.none());
    }
    
  }

  public static class RetryBatch {
    
    private final DeflatedBatch deflatedBatch;
    private final long retryAfterMillis;
    
    public RetryBatch(DeflatedBatch deflatedBatch, long retryAfterMillis) {
      this.deflatedBatch = deflatedBatch;
      this.retryAfterMillis = retryAfterMillis;
    }

    public long getRetryAfterMillis() {
      return retryAfterMillis;
    }
    
    public DeflatedBatch getDeflatedBatch() {
      return deflatedBatch;
    }
    
  }
  
}
