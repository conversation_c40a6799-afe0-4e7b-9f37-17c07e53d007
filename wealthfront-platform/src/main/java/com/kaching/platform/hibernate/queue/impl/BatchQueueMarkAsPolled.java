package com.kaching.platform.hibernate.queue.impl;

import java.util.ArrayList;
import java.util.List;

import org.joda.time.DateTime;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.common.Strings;
import com.kaching.platform.discovery.ServiceId;
import com.kaching.platform.hibernate.Id;
import com.kaching.platform.hibernate.queue.BatchQueueBinder;
import com.kaching.platform.queryengine.AbstractQuery;

public class BatchQueueMarkAsPolled extends AbstractQuery<BatchQueueLockAndExecuteResult> {

  private final String queueName;
  private final Option<ServiceId> newPolledBy;
  private final List<Id<BatchQueueBatch>> batchIds;

  public BatchQueueMarkAsPolled(String queueName, Option<ServiceId> newPolledBy, List<Id<BatchQueueBatch>> batchIds) {
    this.queueName = queueName;
    this.newPolledBy = newPolledBy;
    this.batchIds = batchIds;
  }
  
  @Inject BatchQueueBinder binder;
  @Inject DateTime now;

  @Override
  public BatchQueueLockAndExecuteResult process() {
    return binder.getRunner(queueName).tryLockAndExecute(batchIds, (session, batches) -> {
      List<Id<BatchQueueBatch>> changed = new ArrayList<>();
      List<Id<BatchQueueBatch>> unchanged = new ArrayList<>();
      batches.forEach(batch -> {
        if (newPolledBy.isDefined()) {
          if (batch.getPolledBy().isDefined() && batch.getPolledBy().getOrThrow().equals(newPolledBy.getOrThrow())) {
            unchanged.add(batch.getId());
          } else {
            batch.markAsPolled(now, newPolledBy.getOrThrow());
            changed.add(batch.getId());
          }
        } else {
          if (batch.getPolledBy().isDefined()) {
            batch.markAsNotPolled();
            changed.add(batch.getId());
          } else {
            unchanged.add(batch.getId());
          }
        }
      });
      return Strings.format("Changed: %s,\n Unchanged: %s", changed, unchanged);
    });
  }
  
}
