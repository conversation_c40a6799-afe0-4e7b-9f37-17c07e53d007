package com.kaching.platform.tinykv.impl;

import java.nio.charset.StandardCharsets;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.tinykv.TinyKvStoreId;

public class TinyKvStoreSizeFetcherImpl implements TinyKvStoreSizeFetcher {
  
  @Inject TinyKvRawReader reader;
  @Inject TinyKvRawWriter writer;

  @Override
  public Option<Integer> getSize(TinyKvStoreId storeId) {
    return reader.getSettings(TinyKvSettingsKeys.generateStoreSizeKey(storeId))
        .transform(serialized -> Integer.parseInt(new String(serialized, StandardCharsets.UTF_8)));
  }

  @Override
  public void setSize(TinyKvStoreId storeId, int size) {
    byte[] sizeSerialized = Integer.toString(size).getBytes(StandardCharsets.UTF_8);
    writer.upsertSettingsEntry(1, TinyKvSettingsKeys.generateStoreSizeKey(storeId), sizeSerialized);
  }

}
