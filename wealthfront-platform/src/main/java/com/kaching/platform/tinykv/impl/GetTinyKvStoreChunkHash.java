package com.kaching.platform.tinykv.impl;

import com.google.common.collect.Range;
import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.DbSession;
import com.kaching.platform.hibernate.Transacter;
import com.kaching.platform.hibernate.WithReadOnlySessionExpression;
import com.kaching.platform.queryengine.AbstractQuery;
import com.kaching.platform.tinykv.TinyKvStoreId;
import com.kaching.platform.util.Ranges;
import com.twolattes.json.Entity;

public class GetTinyKvStoreChunkHash extends AbstractQuery<GetTinyKvStoreChunkHash.ChunkHash> {
  
  private final TinyKvStoreId storeId;
  private final Option<String> keyFromIncl;
  private final Option<String> keyToExcl;

  public GetTinyKvStoreChunkHash(TinyKvStoreId storeId, Option<String> keyFromIncl, Option<String> keyToExcl) {
    this.storeId = storeId;
    this.keyFromIncl = keyFromIncl;
    this.keyToExcl = keyToExcl;
  }
  
  @Inject Transacter transacter;

  @Override
  public ChunkHash process() {
    Range<String> keyRange = Ranges.fromInclExclBounds(keyFromIncl, keyToExcl);
    return transacter.execute(new WithReadOnlySessionExpression<ChunkHash>() {
      
      @Inject TinyKvRawReader reader;
      
      @Override
      public ChunkHash run(DbSession session) {
        TinyKvReconChunk reconChunk = reader.getReconChunk(storeId, keyRange);
        return new ChunkHash(reconChunk.hash(), reconChunk.size().getOrThrow());
      }
      
    });
  }

  @Entity 
  public record ChunkHash(int hash, int size) {}

}
