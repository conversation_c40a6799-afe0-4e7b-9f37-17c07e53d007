package com.kaching.entities.converters;

import com.kaching.entities.EmailAddress;
import com.kaching.platform.converters.Converter;

/**
 * Validating, for queries.
 *
 * @see EmailAddressConverter
 */
public class EmailAddressValidatingConverter implements Converter<EmailAddress> {

  public EmailAddress fromString(String representation) {
    return new EmailAddress(representation);
  }

  public String toString(EmailAddress value) {
    return value.toString();
  }

}
