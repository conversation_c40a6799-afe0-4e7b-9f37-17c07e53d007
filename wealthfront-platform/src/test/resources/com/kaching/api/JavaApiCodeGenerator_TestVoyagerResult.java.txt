package com.wealthfront.auto.types.global;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Preconditions;
import com.kaching.api.ExposeTo;
import com.kaching.api.ExposeType;
import com.kaching.platform.common.Option;
import com.kaching.util.id.ExternalId;
import com.twolattes.json.Entity;
import com.twolattes.json.Value;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.navigation.VoyagerStepId;
import java.util.Objects;
import javax.annotation.Nonnull;
import javax.annotation.Nullable;

@ExposeType(
    namespace = ExposeType.RewriteNamespace.DO_NOT_COPY,
    value = {
        ExposeTo.LOCAL,
        ExposeTo.BACKEND,
        ExposeTo.API_SERVER,
        ExposeTo.FRONTEND
    }
)
@Entity
public class TestVoyagerResult {
  @Value(
      nullable = false
  )
  private ExternalId<VoyagerRecord> voyagerRecordId;

  @Value(
      nullable = false
  )
  private VoyagerStepId stepId;

  @Value(
      optional = true,
      nullable = true
  )
  private String stepPath;

  @Value(
      nullable = false
  )
  private TestVoyagerView view;

  public TestVoyagerResult() {
    // JSON
  }

  public TestVoyagerResult(ExternalId<VoyagerRecord> voyagerRecordId, VoyagerStepId stepId,
      String stepPath, TestVoyagerView view) {
    this.voyagerRecordId = voyagerRecordId;
    this.stepId = stepId;
    this.stepPath = stepPath;
    this.view = view;
  }

  public ExternalId<VoyagerRecord> getVoyagerRecordId() {
    return voyagerRecordId;
  }

  public VoyagerStepId getStepId() {
    return stepId;
  }

  public Option<String> getStepPath() {
    return Option.of(stepPath);
  }

  public TestVoyagerView getView() {
    return view;
  }

  public void validate() {
    Preconditions.checkNotNull(voyagerRecordId, "field 'voyagerRecordId' should not be null");
    Preconditions.checkNotNull(stepId, "field 'stepId' should not be null");
    Preconditions.checkNotNull(view, "field 'view' should not be null");
  }

  @Override
  public int hashCode() {
    return Objects.hash(this.voyagerRecordId, this.stepId, this.stepPath, this.view);
  }

  @Override
  public boolean equals(Object o) {
    if (o == this) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TestVoyagerResult that = (TestVoyagerResult) o;
    return Objects.equals(voyagerRecordId, that.voyagerRecordId) &&
        Objects.equals(stepId, that.stepId) &&
        Objects.equals(stepPath, that.stepPath) &&
        Objects.equals(view, that.view);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    boolean isExactClass = this.getClass().equals(TestVoyagerResult.class);
    if (isExactClass) {
      sb.append("TestVoyagerResult {\n");
    }
    sb.append("  voyagerRecordId: ").append(voyagerRecordId).append("\n");
    sb.append("  stepId: ").append(stepId).append("\n");
    sb.append("  stepPath: ").append(stepPath).append("\n");
    sb.append("  view: ").append(view == null ? "null" : view.toString().replaceAll("\n", "\n  ")).append("\n");
    if (isExactClass) {
      sb.append("}");
    }
    return sb.toString();
  }

  public Builder copy() {
    return builder()
      .withVoyagerRecordId(getVoyagerRecordId())
      .withStepId(getStepId())
      .withStepPath(getStepPath().getOrNull())
      .withView(getView());
  }

  public static TestVoyagerResult toGlobal(com.wealthfront.model.TestVoyagerResult model) {
    return builder()
      .withVoyagerRecordId(new ExternalId<VoyagerRecord>(model.getVoyagerRecordId()))
      .withStepId(new VoyagerStepId(model.getStepId()))
      .withStepPath(model.getStepPath())
      .withView(TestVoyagerView.toGlobal(model.getView()))
      .build();
  }

  public com.wealthfront.model.TestVoyagerResult toWFModel() {
    return com.wealthfront.model.TestVoyagerResult.with()
      .voyagerRecordId(this.getVoyagerRecordId().getId())
      .stepId(this.getStepId().getId())
      .stepPath(this.getStepPath().getOrNull())
      .view(this.getView().toWFModel())
      .build();
  }

  public static Builder builder() {
    return new Builder();
  }

  public static class Builder {
    @Nonnull
    private ExternalId<VoyagerRecord> voyagerRecordId = null;

    @Nonnull
    private VoyagerStepId stepId = null;

    @Nullable
    private String stepPath = null;

    @Nonnull
    private TestVoyagerView view = null;

    public Builder withVoyagerRecordId(@Nonnull ExternalId<VoyagerRecord> voyagerRecordId) {
      this.voyagerRecordId = voyagerRecordId;
      return this;
    }

    public Builder withStepId(@Nonnull VoyagerStepId stepId) {
      this.stepId = stepId;
      return this;
    }

    public Builder withStepPath(@Nullable String stepPath) {
      this.stepPath = stepPath;
      return this;
    }

    public Builder withView(@Nonnull TestVoyagerView view) {
      this.view = view;
      return this;
    }

    public TestVoyagerResult build() {
      TestVoyagerResult obj1 = new TestVoyagerResult(voyagerRecordId, stepId, stepPath, view);
      obj1.validate();
      return obj1;
    }

    @VisibleForTesting
    public TestVoyagerResult buildForTesting() {
      TestVoyagerResult obj1 = new TestVoyagerResult(voyagerRecordId, stepId, stepPath, view);
      return obj1;
    }
  }
}
