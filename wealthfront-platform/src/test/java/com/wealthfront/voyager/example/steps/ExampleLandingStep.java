package com.wealthfront.voyager.example.steps;

import static com.wealthfront.voyager.example.navigation.MortgageStepArguments.emptyArguments;

import org.jetbrains.annotations.NotNull;

import com.google.inject.Inject;
import com.kaching.platform.common.Option;
import com.kaching.platform.hibernate.Id;
import com.kaching.util.functional.Either;
import com.wealthfront.voyager.example.navigation.MortgageRoute;
import com.wealthfront.voyager.example.navigation.MortgageStepArguments.EmptyArguments;
import com.wealthfront.voyager.example.payload.ExampleMortgageResult;
import com.wealthfront.voyager.example.payload.StringUpdateRequest;
import com.wealthfront.voyager.example.views.ExampleStartView;
import com.wealthfront.voyager.model.VoyagerRecord;
import com.wealthfront.voyager.navigation.AppCompatibilityRequirement;
import com.wealthfront.voyager.navigation.NavigableVoyagerStep;
import com.wealthfront.voyager.navigation.VoyagerErrors;
import com.wealthfront.voyager.navigation.VoyagerResult;
import com.wealthfront.voyager.navigation.VoyagerRoute;
import com.wealthfront.voyager.navigation.VoyagerStep;
import com.wealthfront.voyager.navigation.VoyagerStepId;

public class ExampleLandingStep implements NavigableVoyagerStep<StringUpdateRequest, EmptyArguments> {

  @Inject MortgageSelectionStep mortgageSelectionStep;

  @Override
  public ResultBuilder<EmptyArguments> createStep(
      Id<VoyagerRecord> voyagerRecordId, VoyagerStepId previousStepId) {
    return new ResultBuilder<>(buildResult(), emptyArguments());
  }

  @Override
  public VoyagerResult<?> resumeStep(
      Id<VoyagerRecord> voyagerRecordId, EmptyArguments stepArguments) {
    return buildResult();
  }

  private ExampleMortgageResult buildResult() {
    return ExampleMortgageResult.builder()
        .withView(new ExampleStartView())
        .build();
  }

  @Override
  public Either<VoyagerStep<?>, VoyagerErrors> updateAndGetNext(Id<VoyagerRecord> id, StringUpdateRequest payload) {
    return Either.left(mortgageSelectionStep);
  }

  @NotNull
  @Override
  public VoyagerRoute getRoute() {
    return MortgageRoute.LANDING;
  }

  @Override
  public AppCompatibilityRequirement getCompatibilityRequirement() {
    return new AppCompatibilityRequirement(Option.some("2024.1.1"), Option.some("2000"));
  }

}
