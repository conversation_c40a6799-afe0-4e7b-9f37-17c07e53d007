package com.wealthfront.voyager.navigation;

import static com.kaching.DefaultKachingMarshallers.createMarshaller;
import static com.twolattes.json.Json.object;
import static com.wealthfront.test.Assert.assertMarshalling;
import static com.wealthfront.test.Assert.assertOptionEmpty;
import static com.wealthfront.test.Assert.assertOptionEquals;
import static org.junit.Assert.assertEquals;

import org.junit.Test;

import com.kaching.util.id.ExternalId;
import com.twolattes.json.Entity;
import com.twolattes.json.Json;
import com.twolattes.json.Marshaller;
import com.twolattes.json.Value;
import com.wealthfront.voyager.example.views.ExampleMortgageView;
import com.wealthfront.voyager.example.views.ExampleView;
import com.wealthfront.voyager.model.VoyagerRecord;

public class AbstractVoyagerResultTest {

  private static final Marshaller<ExampleVoyagerResult> marshaller = createMarshaller(ExampleVoyagerResult.class);

  @Test
  public void marshalling() {
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(new ExampleView("Hello"));
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("applications/{applicationId}/loan-type");

    Json.Value expected = object(
        "voyagerRecordId", "1",
        "stepId", "applications/1/loan-type",
        "stepPath", "applications/{applicationId}/loan-type",
        "view", object(
            "exampleField", "Hello",
            "type", "example-view"
        )
    );

    assertMarshalling(marshaller, expected, result);
  }

  @Test
  public void marshalling_withoutOptional() {
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(new ExampleView("Hello"));
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);

    Json.Value expected = object(
        "voyagerRecordId", "1",
        "stepId", "applications/1/loan-type",
        "view", object(
            "exampleField", "Hello",
            "type", "example-view"
        )
    );

    assertMarshalling(marshaller, expected, result);
  }

  @Test
  public void getters() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);
    result.setStepPathForTesting("applications/{applicationId}/loan-type");

    assertEquals(stepId, result.getStepId());
    assertOptionEquals("applications/{applicationId}/loan-type", result.getStepPath());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals(exampleView, result.getView());
  }

  @Test
  public void getters_withoutOptional() {
    ExampleView exampleView = new ExampleView();
    VoyagerStepId stepId = VoyagerStepId.of("applications/1/loan-type");
    ExampleVoyagerResult result = new ExampleVoyagerResult(exampleView);
    ExternalId<VoyagerRecord> voyagerRecordId = new ExternalId<>("1");

    result.setVoyagerRecordIdForTesting(voyagerRecordId);
    result.setStepIdForTesting(stepId);

    assertEquals(stepId, result.getStepId());
    assertOptionEmpty(result.getStepPath());
    assertEquals(voyagerRecordId, result.getVoyagerRecordId());
    assertEquals(exampleView, result.getView());
  }

  @Entity
  static class ExampleVoyagerResult extends AbstractVoyagerResult<ExampleMortgageView> {

    @Value
    private ExampleMortgageView view;

    ExampleVoyagerResult() { /* JSON */ }

    ExampleVoyagerResult(ExampleMortgageView view) {
      this.view = view;
    }

    @Override
    public ExampleMortgageView getView() {
      return view;
    }

  }

}